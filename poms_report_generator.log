2025-07-07 10:22:24,094 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 10:22:24,096 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 10:22:24,099 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 10:22:24,101 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 10:22:24,102 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 10:22:24,104 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 10:22:24,105 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 10:22:24,108 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 10:22:24,109 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 10:22:24,111 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 10:22:24,111 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 10:22:24,112 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 12:07:54,299 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 12:07:54,303 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 12:07:54,307 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 12:07:54,309 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 12:07:54,310 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 12:07:54,312 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 12:07:54,314 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 12:07:54,317 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 12:07:54,320 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 12:07:54,323 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 12:07:54,323 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 12:07:54,323 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 12:08:18,795 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 12:08:18,798 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 12:08:18,800 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 12:08:18,801 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 12:08:18,802 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 12:08:18,803 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 12:08:18,805 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 12:08:18,808 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 12:08:18,809 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 12:08:18,812 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 12:08:18,812 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 12:08:18,812 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 12:08:39,994 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:08:39,994 - database_manager - ERROR - Connection test failed for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:08:40,120 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 12:55:50,368 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 12:55:50,372 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 12:55:50,375 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 12:55:50,377 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 12:55:50,379 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 12:55:50,381 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 12:55:50,384 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 12:55:50,386 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 12:55:50,388 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 12:55:50,391 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 12:55:50,391 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 12:55:50,391 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 12:56:34,253 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:34,253 - database_manager - ERROR - Connection test failed for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:34,362 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 12:56:44,361 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:44,361 - database_manager - ERROR - Connection test failed for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:44,442 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 12:56:49,628 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:49,629 - database_manager - ERROR - Connection test failed for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:56:49,710 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 12:56:55,951 - report_generator - INFO - Executing Customer Service Performance (Multiple Channel) on cdccrm with parameters: [2025, 5]
2025-07-07 12:57:01,672 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:57:01,673 - database_manager - ERROR - Connection test failed for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:57:01,737 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 12:57:16,977 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:57:16,978 - database_manager - ERROR - Database query error: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 12:57:16,978 - database_manager - ERROR - Query: SELECT case_number,
type_of_incident,
case_status,
contact_mode,
cs_actual_start_datetime,
cs_completed_datetime,
cs_available_duration,
cs_actual_duration
FROM sla_cs
WHERE contact_mode IN ('Open Portal', 'Email', 'Letter Correspondence')
AND YEAR(created_date) = ?
AND MONTH(created_date) = ?
AND deleted = 0
2025-07-07 12:57:16,978 - database_manager - ERROR - Parameters: [2025, 5]
2025-07-07 12:57:16,978 - report_generator - ERROR - Failed to generate Customer Service Performance (Multiple Channel): 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 13:00:55,027 - report_generator - INFO - Executing Customer Service Performance (Telephone) on cdccrm with parameters: [2025, 6]
2025-07-07 13:01:16,101 - database_manager - ERROR - Database connection error for cdccrm: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 13:01:16,102 - database_manager - ERROR - Database query error: 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 13:01:16,102 - database_manager - ERROR - Query: SELECT
date_call AS 'DATE CALL',
acd_call_offer AS 'CALL OFFER',
acd_call_handle AS 'CALL HANDLE',
call_WI_lvl AS 'CALL WITHIN 10',
call_abandon_short AS 'ABANDON SHORT',
call_abandon_long AS 'ABANDON LONG',
ROUND(((call_abandon_long + call_abandon_short) / acd_call_offer) * 100, 2) AS 'ABANDON %',
ROUND((acd_call_handle / acd_call_offer) * 100, 2) AS 'ANSWER %',
ROUND(((call_WI_lvl + call_abandon_short) / acd_call_offer) * 100, 2) AS service_level FROM sla_mitel 
WHERE YEAR(date_call) = ?
AND MONTH(date_call) = ?
ORDER BY date_call ASC;
2025-07-07 13:01:16,102 - database_manager - ERROR - Parameters: [2025, 6]
2025-07-07 13:01:16,102 - report_generator - ERROR - Failed to generate Customer Service Performance (Telephone): 2003 (HY000): Can't connect to MySQL server on '***************:3306' (10060)
2025-07-07 13:04:08,332 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 13:04:08,334 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 13:04:08,336 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 13:04:08,338 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 13:04:08,339 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 13:04:08,339 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 13:04:08,341 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 13:04:08,343 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 13:04:08,345 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 13:04:08,347 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 13:04:08,347 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 13:04:08,347 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 13:04:25,016 - report_generator - INFO - Executing Customer Service Performance (Telephone) on cdc_poms with parameters: [2025, 6]
2025-07-07 13:04:25,173 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 13:04:25,199 - database_manager - ERROR - Unexpected error connecting to cdc_poms: Execution failed on sql 'SELECT
date_call AS 'DATE CALL',
acd_call_offer AS 'CALL OFFER',
acd_call_handle AS 'CALL HANDLE',
call_WI_lvl AS 'CALL WITHIN 10',
call_abandon_short AS 'ABANDON SHORT',
call_abandon_long AS 'ABANDON LONG',
ROUND(((call_abandon_long + call_abandon_short) / acd_call_offer) * 100, 2) AS 'ABANDON %',
ROUND((acd_call_handle / acd_call_offer) * 100, 2) AS 'ANSWER %',
ROUND(((call_WI_lvl + call_abandon_short) / acd_call_offer) * 100, 2) AS service_level FROM sla_mitel 
WHERE YEAR(date_call) = ?
AND MONTH(date_call) = ?
ORDER BY date_call ASC;': Not all parameters were used in the SQL statement
2025-07-07 13:04:25,203 - database_manager - ERROR - Unexpected error executing query: Execution failed on sql 'SELECT
date_call AS 'DATE CALL',
acd_call_offer AS 'CALL OFFER',
acd_call_handle AS 'CALL HANDLE',
call_WI_lvl AS 'CALL WITHIN 10',
call_abandon_short AS 'ABANDON SHORT',
call_abandon_long AS 'ABANDON LONG',
ROUND(((call_abandon_long + call_abandon_short) / acd_call_offer) * 100, 2) AS 'ABANDON %',
ROUND((acd_call_handle / acd_call_offer) * 100, 2) AS 'ANSWER %',
ROUND(((call_WI_lvl + call_abandon_short) / acd_call_offer) * 100, 2) AS service_level FROM sla_mitel 
WHERE YEAR(date_call) = ?
AND MONTH(date_call) = ?
ORDER BY date_call ASC;': Not all parameters were used in the SQL statement
2025-07-07 13:04:25,204 - report_generator - ERROR - Failed to generate Customer Service Performance (Telephone): Execution failed on sql 'SELECT
date_call AS 'DATE CALL',
acd_call_offer AS 'CALL OFFER',
acd_call_handle AS 'CALL HANDLE',
call_WI_lvl AS 'CALL WITHIN 10',
call_abandon_short AS 'ABANDON SHORT',
call_abandon_long AS 'ABANDON LONG',
ROUND(((call_abandon_long + call_abandon_short) / acd_call_offer) * 100, 2) AS 'ABANDON %',
ROUND((acd_call_handle / acd_call_offer) * 100, 2) AS 'ANSWER %',
ROUND(((call_WI_lvl + call_abandon_short) / acd_call_offer) * 100, 2) AS service_level FROM sla_mitel 
WHERE YEAR(date_call) = ?
AND MONTH(date_call) = ?
ORDER BY date_call ASC;': Not all parameters were used in the SQL statement
2025-07-07 14:20:32,720 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 14:20:32,725 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 14:20:32,730 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 14:20:32,732 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 14:20:32,734 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 14:20:32,736 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 14:20:32,740 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 14:20:32,744 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 14:20:32,747 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 14:20:32,750 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 14:20:32,750 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 14:20:32,751 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 14:20:32,940 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-07 14:31:02,954 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 14:31:02,957 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 14:31:02,960 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 14:31:02,962 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 14:31:02,963 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 14:31:02,965 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 14:31:02,967 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 14:31:02,970 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 14:31:02,972 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 14:31:02,975 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 14:31:02,976 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 14:31:02,976 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 14:31:18,853 - report_generator - INFO - Executing Customer Service Performance (Multiple Channel) on cdc_poms with parameters: [2025, 5]
2025-07-07 14:31:19,180 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:20,326 - database_manager - INFO - Query executed successfully in 1.15 seconds. Rows returned: 5164
2025-07-07 14:31:21,428 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Multiple Channel)_MAY2025.xlsx
2025-07-07 14:31:21,549 - report_generator - INFO - Executing Customer Service Performance (Telephone) on cdc_poms with parameters: [2025, 5]
2025-07-07 14:31:21,596 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:21,611 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 31
2025-07-07 14:31:21,627 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Telephone)_MAY2025.xlsx
2025-07-07 14:31:21,634 - report_generator - INFO - Executing Incident Management (Case Acknowledgement) on cdc_poms with parameters: [2025, 5, 2025, 5]
2025-07-07 14:31:21,695 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:21,850 - database_manager - INFO - Query executed successfully in 0.15 seconds. Rows returned: 1431
2025-07-07 14:31:22,039 - report_generator - INFO - Report saved successfully: output\Incident Management (Case Acknowledgement)_MAY2025.xlsx
2025-07-07 14:31:22,051 - report_generator - INFO - Executing Incident Management (RIT) on cdc_poms with parameters: [2025, 5, 2025, 5]
2025-07-07 14:31:22,094 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:22,273 - database_manager - INFO - Query executed successfully in 0.18 seconds. Rows returned: 1435
2025-07-07 14:31:22,437 - report_generator - INFO - Report saved successfully: output\Incident Management (RIT)_MAY2025.xlsx
2025-07-07 14:31:22,445 - report_generator - INFO - Executing Incident Management S123 on cdc_poms with parameters: [2025, 5, 2025, 5, 2025, 5]
2025-07-07 14:31:22,498 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:22,627 - database_manager - INFO - Query executed successfully in 0.13 seconds. Rows returned: 1380
2025-07-07 14:31:22,806 - report_generator - INFO - Report saved successfully: output\Incident Management S123_MAY2025.xlsx
2025-07-07 14:31:22,818 - report_generator - INFO - Executing Incident Management S4 on cdc_poms with parameters: [2025, 5, 2025, 5, 2025]
2025-07-07 14:31:22,872 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:22,893 - database_manager - ERROR - Database connection error for cdc_poms: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 14:31:22,898 - database_manager - ERROR - Database query error: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 14:31:22,899 - database_manager - ERROR - Query: SELECT DISTINCT 
c.case_number AS case_number,
c.redmine_number AS redmine_number,
c.name AS case_title,
approver_t.name AS task_title,
approver_tc.sla_task_flag_c AS itapprover_sla_flag,
CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_actual_start_datetime,
CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00') AS itapprover_completed_datetime,
approver_tc.task_duration_c AS itapprover_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.date_due,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_available_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_actual_duration,
UPPER(c.redmine_implementation_issue) AS implementation_issue,
approver_t.child_parent_redmine AS child_parent_redmine,
GREATEST((SELECT itseverity_actual_duration - itseverity_available_duration), 0) AS exceed_duration
--  approver_tc.is_sent
FROM ((((cases c 
JOIN cases_cstm cc ON((c.id = cc.id_c))) 
LEFT JOIN tasks approver_t ON((c.id = approver_t.parent_id))) 
LEFT JOIN tasks_cstm approver_tc ON(((approver_t.id = approver_tc.id_c) 
AND (approver_tc.sla_task_flag_c = '4')))) 
LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
AND (subcat.type_code = 'cdc_sub_category_list') 
AND (TRIM(subcat.value_code) <> '') 
AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))
JOIN users us ON us.id=approver_t.created_by) 
WHERE ((cc.incident_service_type_c = 'incident_it') 
AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
AND (approver_t.status = 'Completed') 
AND (cc.request_type_c = 'incident')
AND (c.deleted = 0)
AND (approver_t.deleted = 0)
AND (approver_tc.task_number_c IS NOT NULL)
AND (approver_t.date_start IS NOT NULL)
AND (approver_t.reassign_time_c IS NOT NULL)
AND (TRIM(subcat.value_code) <> '')) 
AND (approver_t.child_parent_redmine NOT IN ('child') OR approver_t.child_parent_redmine IS NULL)
AND (STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(approver_tc.sla_stop_approver_c,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
AND (approver_tc.is_sent = 1)
-- and (c.case_number in (3988535))
AND (c.case_number NOT IN (3978672,3990412,3988725,3989233,3989437,3990293,3990257,3990112,3987449,3989837))
-- (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') <= '2020-08-09')
ORDER BY c.case_number DESC;
2025-07-07 14:31:22,899 - database_manager - ERROR - Parameters: [2025, 5, 2025, 5, 2025]
2025-07-07 14:31:22,900 - report_generator - ERROR - Failed to generate Incident Management S4: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 14:31:22,912 - report_generator - INFO - Executing Service Request on cdc_poms with parameters: [2025, 5]
2025-07-07 14:31:22,961 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:22,974 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 0
2025-07-07 14:31:22,989 - report_generator - INFO - Report saved successfully: output\Service Request_MAY2025.xlsx
2025-07-07 14:31:22,997 - report_generator - INFO - Executing System Availability (Infra) on cdc_poms with parameters: [2025, 5]
2025-07-07 14:31:23,042 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:31:24,718 - database_manager - INFO - Query executed successfully in 1.68 seconds. Rows returned: 0
2025-07-07 14:31:24,736 - report_generator - INFO - Report saved successfully: output\System Availability (Infra)_MAY2025.xlsx
2025-07-07 14:31:24,745 - report_generator - INFO - Executing System Performance (Response Time) on cdc_poms with parameters: [2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5]
2025-07-07 14:31:24,809 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 14:32:29,378 - database_manager - INFO - Query executed successfully in 64.57 seconds. Rows returned: 16
2025-07-07 14:32:29,398 - report_generator - INFO - Report saved successfully: output\System Performance (Response Time)_MAY2025.xlsx
2025-07-07 15:24:34,639 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-07 15:24:34,642 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-07 15:24:34,644 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-07 15:24:34,646 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-07 15:24:34,647 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-07 15:24:34,648 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-07 15:24:34,650 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-07 15:24:34,652 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-07 15:24:34,654 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-07 15:24:34,656 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-07 15:24:34,656 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-07 15:24:34,656 - report_generator - INFO - Loaded 9 report definitions
2025-07-07 15:24:45,700 - report_generator - INFO - Executing Customer Service Performance (Multiple Channel) on cdc_poms with parameters: [2025, 5]
2025-07-07 15:24:45,879 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:46,966 - database_manager - INFO - Query executed successfully in 1.09 seconds. Rows returned: 5164
2025-07-07 15:24:47,694 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Multiple Channel)_MAY2025.xlsx
2025-07-07 15:24:47,799 - report_generator - INFO - Executing Customer Service Performance (Telephone) on cdc_poms with parameters: [2025, 5]
2025-07-07 15:24:47,863 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:47,877 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 31
2025-07-07 15:24:47,896 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Telephone)_MAY2025.xlsx
2025-07-07 15:24:47,903 - report_generator - INFO - Executing Incident Management (Case Acknowledgement) on cdc_poms with parameters: [2025, 5, 2025, 5]
2025-07-07 15:24:47,973 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:48,150 - database_manager - INFO - Query executed successfully in 0.18 seconds. Rows returned: 1431
2025-07-07 15:24:48,359 - report_generator - INFO - Report saved successfully: output\Incident Management (Case Acknowledgement)_MAY2025.xlsx
2025-07-07 15:24:48,367 - report_generator - INFO - Executing Incident Management (RIT) on cdc_poms with parameters: [2025, 5, 2025, 5]
2025-07-07 15:24:48,433 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:48,588 - database_manager - INFO - Query executed successfully in 0.15 seconds. Rows returned: 1435
2025-07-07 15:24:48,758 - report_generator - INFO - Report saved successfully: output\Incident Management (RIT)_MAY2025.xlsx
2025-07-07 15:24:48,769 - report_generator - INFO - Executing Incident Management S123 on cdc_poms with parameters: [5, 2025, 5, 2025, 5, 2025]
2025-07-07 15:24:48,857 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:48,971 - database_manager - INFO - Query executed successfully in 0.11 seconds. Rows returned: 0
2025-07-07 15:24:48,986 - report_generator - INFO - Report saved successfully: output\Incident Management S123_MAY2025.xlsx
2025-07-07 15:24:48,994 - report_generator - INFO - Executing Incident Management S4 on cdc_poms with parameters: [5, 2025, 5, 2025, 5]
2025-07-07 15:24:49,073 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:49,083 - database_manager - ERROR - Database connection error for cdc_poms: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 15:24:49,088 - database_manager - ERROR - Database query error: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 15:24:49,089 - database_manager - ERROR - Query: SELECT DISTINCT 
c.case_number AS case_number,
c.redmine_number AS redmine_number,
c.name AS case_title,
approver_t.name AS task_title,
approver_tc.sla_task_flag_c AS itapprover_sla_flag,
CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_actual_start_datetime,
CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00') AS itapprover_completed_datetime,
approver_tc.task_duration_c AS itapprover_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.date_due,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_available_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_actual_duration,
UPPER(c.redmine_implementation_issue) AS implementation_issue,
approver_t.child_parent_redmine AS child_parent_redmine,
GREATEST((SELECT itseverity_actual_duration - itseverity_available_duration), 0) AS exceed_duration
--  approver_tc.is_sent
FROM ((((cases c 
JOIN cases_cstm cc ON((c.id = cc.id_c))) 
LEFT JOIN tasks approver_t ON((c.id = approver_t.parent_id))) 
LEFT JOIN tasks_cstm approver_tc ON(((approver_t.id = approver_tc.id_c) 
AND (approver_tc.sla_task_flag_c = '4')))) 
LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
AND (subcat.type_code = 'cdc_sub_category_list') 
AND (TRIM(subcat.value_code) <> '') 
AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))
JOIN users us ON us.id=approver_t.created_by) 
WHERE ((cc.incident_service_type_c = 'incident_it') 
AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
AND (approver_t.status = 'Completed') 
AND (cc.request_type_c = 'incident')
AND (c.deleted = 0)
AND (approver_t.deleted = 0)
AND (approver_tc.task_number_c IS NOT NULL)
AND (approver_t.date_start IS NOT NULL)
AND (approver_t.reassign_time_c IS NOT NULL)
AND (TRIM(subcat.value_code) <> '')) 
AND (approver_t.child_parent_redmine NOT IN ('child') OR approver_t.child_parent_redmine IS NULL)
AND (STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(approver_tc.sla_stop_approver_c,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
AND (approver_tc.is_sent = 1)
-- and (c.case_number in (3988535))
AND (c.case_number NOT IN (3978672,3990412,3988725,3989233,3989437,3990293,3990257,3990112,3987449,3989837))
-- (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') <= '2020-08-09')
ORDER BY c.case_number DESC;
2025-07-07 15:24:49,090 - database_manager - ERROR - Parameters: [5, 2025, 5, 2025, 5]
2025-07-07 15:24:49,090 - report_generator - ERROR - Failed to generate Incident Management S4: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-07 15:24:49,099 - report_generator - INFO - Executing Service Request on cdc_poms with parameters: [2025, 5]
2025-07-07 15:24:49,152 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:49,162 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 0
2025-07-07 15:24:49,173 - report_generator - INFO - Report saved successfully: output\Service Request_MAY2025.xlsx
2025-07-07 15:24:49,179 - report_generator - INFO - Executing System Availability (Infra) on cdc_poms with parameters: [2025, 5]
2025-07-07 15:24:49,232 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:24:50,875 - database_manager - INFO - Query executed successfully in 1.64 seconds. Rows returned: 0
2025-07-07 15:24:50,891 - report_generator - INFO - Report saved successfully: output\System Availability (Infra)_MAY2025.xlsx
2025-07-07 15:24:50,898 - report_generator - INFO - Executing System Performance (Response Time) on cdc_poms with parameters: [5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025, 5, 2025]
2025-07-07 15:24:50,953 - database_manager - INFO - Executing query on cdc_poms
2025-07-07 15:25:52,451 - database_manager - INFO - Query executed successfully in 61.50 seconds. Rows returned: 16
2025-07-07 15:25:52,505 - report_generator - INFO - Report saved successfully: output\System Performance (Response Time)_MAY2025.xlsx
2025-07-07 22:55:08,928 - gui_application - INFO - Application interrupted by user
2025-07-08 10:05:17,184 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-08 10:05:17,187 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Multiple Channel)
2025-07-08 10:05:17,190 - birt_parser - INFO - Successfully parsed report: Customer Service Performance (Telephone)
2025-07-08 10:05:17,191 - birt_parser - INFO - Successfully parsed report: Incident Management (Case Acknowledgement)
2025-07-08 10:05:17,192 - birt_parser - INFO - Successfully parsed report: Incident Management (RIT)
2025-07-08 10:05:17,194 - birt_parser - INFO - Successfully parsed report: Incident Management S123
2025-07-08 10:05:17,195 - birt_parser - INFO - Successfully parsed report: Incident Management S4
2025-07-08 10:05:17,198 - birt_parser - INFO - Successfully parsed report: Service Request
2025-07-08 10:05:17,199 - birt_parser - INFO - Successfully parsed report: System Availability (Infra)
2025-07-08 10:05:17,201 - birt_parser - INFO - Successfully parsed report: System Performance (Response Time)
2025-07-08 10:05:17,201 - birt_parser - INFO - Parsed 9 reports from knowage
2025-07-08 10:05:17,201 - report_generator - INFO - Loaded 9 report definitions
2025-07-08 10:05:30,613 - report_generator - INFO - Executing Customer Service Performance (Multiple Channel) on cdc_poms with parameters: [2025, 6]
2025-07-08 10:05:30,931 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:31,993 - database_manager - INFO - Query executed successfully in 1.06 seconds. Rows returned: 4597
2025-07-08 10:05:32,738 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Multiple Channel)_JUN2025.xlsx
2025-07-08 10:05:32,862 - report_generator - INFO - Executing Customer Service Performance (Telephone) on cdc_poms with parameters: [2025, 6]
2025-07-08 10:05:32,931 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:32,943 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 30
2025-07-08 10:05:32,962 - report_generator - INFO - Report saved successfully: output\Customer Service Performance (Telephone)_JUN2025.xlsx
2025-07-08 10:05:32,969 - report_generator - INFO - Executing Incident Management (Case Acknowledgement) on cdc_poms with parameters: [2025, 6, 2025, 6]
2025-07-08 10:05:33,018 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:33,165 - database_manager - INFO - Query executed successfully in 0.15 seconds. Rows returned: 1289
2025-07-08 10:05:33,370 - report_generator - INFO - Report saved successfully: output\Incident Management (Case Acknowledgement)_JUN2025.xlsx
2025-07-08 10:05:33,376 - report_generator - INFO - Executing Incident Management (RIT) on cdc_poms with parameters: [2025, 6, 2025, 6]
2025-07-08 10:05:33,431 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:33,557 - database_manager - INFO - Query executed successfully in 0.13 seconds. Rows returned: 1254
2025-07-08 10:05:33,716 - report_generator - INFO - Report saved successfully: output\Incident Management (RIT)_JUN2025.xlsx
2025-07-08 10:05:33,723 - report_generator - INFO - Executing Incident Management S123 on cdc_poms with parameters: [6, 2025, 6, 2025, 6, 2025]
2025-07-08 10:05:33,784 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:33,877 - database_manager - INFO - Query executed successfully in 0.09 seconds. Rows returned: 0
2025-07-08 10:05:33,896 - report_generator - INFO - Report saved successfully: output\Incident Management S123_JUN2025.xlsx
2025-07-08 10:05:33,903 - report_generator - INFO - Executing Incident Management S4 on cdc_poms with parameters: [6, 2025, 6, 2025, 6]
2025-07-08 10:05:33,965 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:34,006 - database_manager - ERROR - Database connection error for cdc_poms: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-08 10:05:34,012 - database_manager - ERROR - Database query error: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-08 10:05:34,012 - database_manager - ERROR - Query: SELECT DISTINCT 
c.case_number AS case_number,
c.redmine_number AS redmine_number,
c.name AS case_title,
approver_t.name AS task_title,
approver_tc.sla_task_flag_c AS itapprover_sla_flag,
CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_actual_start_datetime,
CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00') AS itapprover_completed_datetime,
approver_tc.task_duration_c AS itapprover_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.date_due,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_available_duration,
DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itseverity_actual_duration,
UPPER(c.redmine_implementation_issue) AS implementation_issue,
approver_t.child_parent_redmine AS child_parent_redmine,
GREATEST((SELECT itseverity_actual_duration - itseverity_available_duration), 0) AS exceed_duration
--  approver_tc.is_sent
FROM ((((cases c 
JOIN cases_cstm cc ON((c.id = cc.id_c))) 
LEFT JOIN tasks approver_t ON((c.id = approver_t.parent_id))) 
LEFT JOIN tasks_cstm approver_tc ON(((approver_t.id = approver_tc.id_c) 
AND (approver_tc.sla_task_flag_c = '4')))) 
LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
AND (subcat.type_code = 'cdc_sub_category_list') 
AND (TRIM(subcat.value_code) <> '') 
AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))
JOIN users us ON us.id=approver_t.created_by) 
WHERE ((cc.incident_service_type_c = 'incident_it') 
AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
AND (approver_t.status = 'Completed') 
AND (cc.request_type_c = 'incident')
AND (c.deleted = 0)
AND (approver_t.deleted = 0)
AND (approver_tc.task_number_c IS NOT NULL)
AND (approver_t.date_start IS NOT NULL)
AND (approver_t.reassign_time_c IS NOT NULL)
AND (TRIM(subcat.value_code) <> '')) 
AND (approver_t.child_parent_redmine NOT IN ('child') OR approver_t.child_parent_redmine IS NULL)
AND (STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
-- AND (STR_TO_DATE(CONVERT_TZ(approver_tc.sla_stop_approver_c,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
AND (approver_tc.is_sent = 1)
-- and (c.case_number in (3988535))
AND (c.case_number NOT IN (3978672,3990412,3988725,3989233,3989437,3990293,3990257,3990112,3987449,3989837))
-- (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') <= '2020-08-09')
ORDER BY c.case_number DESC;
2025-07-08 10:05:34,013 - database_manager - ERROR - Parameters: [6, 2025, 6, 2025, 6]
2025-07-08 10:05:34,013 - report_generator - ERROR - Failed to generate Incident Management S4: 1146 (42S02): Table 'cdc_poms.cases' doesn't exist
2025-07-08 10:05:34,020 - report_generator - INFO - Executing Service Request on cdc_poms with parameters: [2025, 6]
2025-07-08 10:05:34,072 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:34,082 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 0
2025-07-08 10:05:34,100 - report_generator - INFO - Report saved successfully: output\Service Request_JUN2025.xlsx
2025-07-08 10:05:34,108 - report_generator - INFO - Executing System Availability (Infra) on cdc_poms with parameters: [2025, 6]
2025-07-08 10:05:34,164 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:05:35,824 - database_manager - INFO - Query executed successfully in 1.66 seconds. Rows returned: 0
2025-07-08 10:05:35,847 - report_generator - INFO - Report saved successfully: output\System Availability (Infra)_JUN2025.xlsx
2025-07-08 10:05:35,853 - report_generator - INFO - Executing System Performance (Response Time) on cdc_poms with parameters: [6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025]
2025-07-08 10:05:35,969 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:06:37,674 - database_manager - INFO - Query executed successfully in 61.70 seconds. Rows returned: 16
2025-07-08 10:06:37,718 - report_generator - INFO - Report saved successfully: output\System Performance (Response Time)_JUN2025.xlsx
2025-07-08 10:14:52,356 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-08 10:14:52,357 - independent_report_generator - INFO - Initialized independent report generator with 9 reports
2025-07-08 10:17:37,996 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-08 10:17:37,997 - independent_report_generator - INFO - Initialized independent report generator with 9 reports
2025-07-08 10:17:38,328 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-08 10:27:46,648 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-08 10:27:46,648 - independent_report_generator - INFO - Initialized independent report generator with 9 reports
2025-07-08 10:27:57,582 - independent_report_generator - INFO - Executing Customer Service Performance (Multiple Channel) on cdc_poms with parameters: [2025, 6]
2025-07-08 10:27:57,929 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:27:59,071 - database_manager - INFO - Query executed successfully in 1.14 seconds. Rows returned: 4597
2025-07-08 10:27:59,868 - independent_report_generator - INFO - Report saved successfully: output\Customer Service Performance (Multiple Channel)_JUN2025.xlsx
2025-07-08 10:28:00,022 - independent_report_generator - INFO - Executing Customer Service Performance (Telephone) on cdc_poms with parameters: [2025, 6]
2025-07-08 10:28:00,079 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:00,093 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 30
2025-07-08 10:28:00,119 - independent_report_generator - INFO - Report saved successfully: output\Customer Service Performance (Telephone)_JUN2025.xlsx
2025-07-08 10:28:00,124 - independent_report_generator - INFO - Executing Incident Management (Case Acknowledgement) on cdc_poms with parameters: [2025, 6, 2025, 6]
2025-07-08 10:28:00,196 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:00,349 - database_manager - INFO - Query executed successfully in 0.15 seconds. Rows returned: 1289
2025-07-08 10:28:00,562 - independent_report_generator - INFO - Report saved successfully: output\Incident Management (Case Acknowledgement)_JUN2025.xlsx
2025-07-08 10:28:00,567 - independent_report_generator - INFO - Executing Incident Management (RIT) on cdc_poms with parameters: [2025, 6, 2025, 6]
2025-07-08 10:28:00,635 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:00,783 - database_manager - INFO - Query executed successfully in 0.15 seconds. Rows returned: 1254
2025-07-08 10:28:00,991 - independent_report_generator - INFO - Report saved successfully: output\Incident Management (RIT)_JUN2025.xlsx
2025-07-08 10:28:00,997 - independent_report_generator - INFO - Executing Incident Management S123 on cdc_poms with parameters: [2025, 6, 2025, 6, 2025, 6]
2025-07-08 10:28:01,067 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:01,195 - database_manager - INFO - Query executed successfully in 0.13 seconds. Rows returned: 1234
2025-07-08 10:28:01,382 - independent_report_generator - INFO - Report saved successfully: output\Incident Management S123_JUN2025.xlsx
2025-07-08 10:28:01,389 - independent_report_generator - INFO - Executing Incident Management S4 on cdc_poms with parameters: [2025, 6, 2025, 6, 2025]
2025-07-08 10:28:01,452 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:01,456 - database_manager - WARNING - Parameter count mismatch: query has 2 placeholders, but 5 parameters provided. Adjusting...
2025-07-08 10:28:01,465 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 3
2025-07-08 10:28:01,483 - independent_report_generator - INFO - Report saved successfully: output\Incident Management S4_JUN2025.xlsx
2025-07-08 10:28:01,488 - independent_report_generator - INFO - Executing Service Request on cdc_poms with parameters: [2025, 6]
2025-07-08 10:28:01,544 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:01,556 - database_manager - INFO - Query executed successfully in 0.01 seconds. Rows returned: 0
2025-07-08 10:28:01,571 - independent_report_generator - INFO - Report saved successfully: output\Service Request_JUN2025.xlsx
2025-07-08 10:28:01,576 - independent_report_generator - INFO - Executing System Availability (Infra) on cdc_poms with parameters: [6, 2025]
2025-07-08 10:28:01,624 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:28:03,533 - database_manager - INFO - Query executed successfully in 1.91 seconds. Rows returned: 126
2025-07-08 10:28:03,576 - independent_report_generator - INFO - Report saved successfully: output\System Availability (Infra)_JUN2025.xlsx
2025-07-08 10:28:03,585 - independent_report_generator - INFO - Executing System Performance (Response Time) on cdc_poms with parameters: [6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025, 6, 2025]
2025-07-08 10:28:03,682 - database_manager - INFO - Executing query on cdc_poms
2025-07-08 10:29:04,689 - database_manager - INFO - Query executed successfully in 61.01 seconds. Rows returned: 16
2025-07-08 10:29:04,729 - independent_report_generator - INFO - Report saved successfully: output\System Performance (Response Time)_JUN2025.xlsx
2025-07-08 10:29:30,077 - database_manager - INFO - Connection test successful for cdc_poms
2025-07-08 11:06:01,253 - database_manager - INFO - Configuration loaded from config.yaml
2025-07-08 11:06:01,253 - independent_report_generator - INFO - Initialized independent report generator with 9 reports
